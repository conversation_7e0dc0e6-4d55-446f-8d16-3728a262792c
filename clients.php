<?php
session_start();
require_once 'config.php';

// Set page title
$pageTitle = 'CLIENT LIST';

// Use a different function name to avoid conflicts
function getClientsWithPolicyData() {
    global $pdo;
    $clients = [];

    try {
        // Get all clients with policy counts in a single query
        $query = "SELECT
            c.*,
            COUNT(p.policy_id) as total_policies,
            COUNT(CASE WHEN LOWER(TRIM(p.status)) = 'active' THEN 1 END) as active_policies,
            COUNT(CASE WHEN LOWER(TRIM(p.status)) = 'pending' THEN 1 END) as pending_policies
            FROM clients c
            LEFT JOIN policies p ON c.client_id = p.client_id
            GROUP BY c.client_id, c.name, c.ic_number, c.client_number, c.gender, c.date_of_birth,
                     c.phone_number, c.email, c.address, c.marital_status, c.race, c.religion,
                     c.nationality, c.occupation, c.exact_duties, c.nature_of_business,
                     c.salary_yearly, c.company_name, c.company_address, c.weight, c.height,
                     c.smoker, c.hospital_admission_history, c.status, c.policy_id, c.created_at, c.updated_at
            ORDER BY c.created_at DESC";

        $stmt = $pdo->query($query);
        $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $clients;

    } catch (PDOException $e) {
        error_log("Error in getClientsWithPolicyData: " . $e->getMessage());
        return [];
    }
}

// Get all clients with our new function
$clients = getClientsWithPolicyData();

// Include the layout header
include 'layout.php';

// Display any error messages
if (isset($_GET['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            ' . htmlspecialchars($_GET['error']) . '
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
          </div>';
}
?>

<!--<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="text-dark font-weight-bold">Clients</h2>
    <a href="client_add.php" class="btn btn-success">
        <i class="fas fa-plus mr-2"></i> Add New Client
    </a>
</div>-->

<div class="card shadow-sm">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr class="bg-light">
                        <th class="px-4 py-3">Name</th>
                        <th class="px-4 py-3">Email</th>
                        <th class="px-4 py-3">Phone</th>
                        <th class="px-4 py-3">Status</th>
                        <th class="px-4 py-3 text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($clients)): ?>
                        <tr>
                            <td colspan="5" class="text-center py-4">No clients found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($clients as $client): ?>
                            <tr data-client-id="<?php echo $client['client_id']; ?>">
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['name']); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['email']); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['phone_number']); ?></td>
                                <td class="px-4 py-3">
                                    <?php 
                                    // Make sure the keys exist with default values to avoid warnings
                                    $activePolicies = isset($client['active_policies']) ? (int)$client['active_policies'] : 0;
                                    $pendingPolicies = isset($client['pending_policies']) ? (int)$client['pending_policies'] : 0;
                                    $totalPolicies = isset($client['total_policies']) ? (int)$client['total_policies'] : 0;
                                    
                                    // Determine client status based on policy statuses
                                    if ($totalPolicies > 0) {
                                        if ($activePolicies > 0) {
                                            echo '<span class="badge badge-success">Active</span>';
                                        } elseif ($pendingPolicies > 0) {
                                            echo '<span class="badge badge-warning">Pending</span>';
                                        } else {
                                            echo '<span class="badge badge-info">Other</span>';
                                        }
                                    } else {
                                        echo '<span class="badge badge-secondary">No Policies</span>';
                                    }
                                    ?>
                                    
                                    <?php if ($totalPolicies > 0): ?>
                                        <div class="policy-details mt-1">
                                            <?php if ($activePolicies > 0): ?>
                                                <span class="badge badge-outline-success">
                                                    <?php echo $activePolicies; ?> Active
                                                </span>
                                            <?php endif; ?>
                                            
                                            <?php if ($pendingPolicies > 0): ?>
                                                <span class="badge badge-outline-warning ml-1">
                                                    <?php echo $pendingPolicies; ?> Pending
                                                </span>
                                            <?php endif; ?>
                                            
                                            <?php 
                                            $otherPolicies = $totalPolicies - $activePolicies - $pendingPolicies;
                                            if ($otherPolicies > 0): 
                                            ?>
                                                <span class="badge badge-outline-info ml-1">
                                                    <?php echo $otherPolicies; ?> Other
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-4 py-3 text-center">
                                    <a href="client_view.php?id=<?php echo $client['client_id']; ?>" class="btn btn-sm btn-info px-3">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>  
    </div>
</div>



<style>
    .table {
        font-size: 0.95rem;
    }
    .table thead tr {
        border-bottom: 2px solid #e0e0e0;
    }
    .table td, .table th {
        vertical-align: middle;
        border-top: 1px solid #e9ecef;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
    .card {
        border: none;
        border-radius: 10px;
        overflow: hidden;
    }
    .bg-light {
        background-color: #f8f9fa !important;
    }

    /* Action buttons styling */
    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
    }

    .action-btn {
        width: 48px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        border: none;
        color: white;
        transition: all 0.2s ease;
        text-decoration: none;
        cursor: pointer;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
        opacity: 0.9;
    }

    .action-btn i {
        font-size: 13px;
    }

    .action-btn.view {
        background-color: #17a2b8;
    }

    .action-btn.edit {
        background-color: #6c757d;
    }

    .action-btn.delete {
        background-color: #dc3545;
    }

    /* Status badge styling */
    .badge {
        font-size: 0.85rem;
        padding: 0.4em 0.8em;
        font-weight: 500;
        border-radius: 4px;
    }

    .badge-success {
        background-color: #e8f5e9;
        color: #2e7d32;
    }

    .badge-secondary {
        background-color: #f5f5f5;
        color: #616161;
    }

    .badge-warning {
        background-color: #fff3e0;
        color: #e65100;
    }

    .badge-info {
        background-color: #e3f2fd;
        color: #0d47a1;
    }

    .gap-2 {
        gap: 0.5rem !important;
    }
    .modal-content {
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .modal-header {
        border-bottom: none;
        padding: 20px 25px;
    }
    .modal-title {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
    }
    .modal-body {
        padding: 20px 25px;
    }
    .modal-body p {
        margin-bottom: 10px;
        font-size: 16px;
    }
    .modal-body .text-danger {
        color: #dc3545;
        font-size: 14px;
    }
    .modal-footer {
        border-top: none;
        padding: 15px 25px 20px;
    }
    .btn-secondary {
        background-color: #6c757d;
        border: none;
        padding: 8px 20px;
        font-weight: 500;
        border-radius: 6px;
    }
    .btn-danger {
        background-color: #dc3545;
        border: none;
        padding: 8px 20px;
        font-weight: 500;
        border-radius: 6px;
    }
    .close {
        font-size: 1.5rem;
        opacity: 0.5;
        transition: opacity 0.2s;
    }
    .close:hover {
        opacity: 1;
    }

    /* New outline badge styles */
    .policy-details {
        margin-top: 5px;
    }
    
    .badge-outline-success {
        background-color: transparent;
        border: 1px solid #28a745;
        color: #28a745;
        font-size: 0.75rem;
    }
    
    .badge-outline-warning {
        background-color: transparent;
        border: 1px solid #ffc107;
        color: #e65100;
        font-size: 0.75rem;
    }
    
    .badge-outline-info {
        background-color: transparent;
        border: 1px solid #17a2b8;
        color: #0d47a1;
        font-size: 0.75rem;
    }
    
    .mt-1 {
        margin-top: 0.25rem;
    }
</style>



<?php include 'layout_footer.php'; ?> 