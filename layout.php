<?php
// layout.php - Combined layout with sidebar and content area
// This file should be included in all pages after session_start()
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? 'CRM System'; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/jquery-ui@1.13.2/themes/base/jquery-ui.min.css" rel="stylesheet">
    <style>
        body {
            padding: 0;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f4f8;
        }
        .wrapper {
            display: flex;
        }
        .sidebar {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%); /* Professional dark theme */
            width: 280px;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 1000;
            box-shadow: 4px 0 15px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
        }
        .logo-container {
            padding: 25px 15px;
            text-align: center;
            background: transparent;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 10px;
        }
        .logo-container img {
            max-width: 75%;
            height: auto;
            filter: brightness(0) invert(1); /* Makes the logo white */
            transition: transform 0.3s ease, opacity 0.3s ease;
            opacity: 0.95;
        }
        .logo-container img:hover {
            transform: scale(1.02);
            opacity: 1;
        }
        /* Search bar styles */
        .sidebar-search {
            padding: 0 15px 15px;
            position: relative;
        }
        .sidebar-search input {
            width: 100%;
            background: rgba(255,255,255,0.1);
            border: none;
            border-radius: 20px;
            padding: 10px 15px 10px 40px;
            color: white;
            transition: all 0.3s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .sidebar-search input:focus {
            background: rgba(255,255,255,0.2);
            outline: none;
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
            color: white;
        }
        .sidebar-search i {
            position: absolute;
            left: 25px;
            top: 12px;
            color: rgba(255,255,255,0.7);
            z-index: 5;
        }
        .sidebar-search input::placeholder {
            color: rgba(255,255,255,0.6);
        }
        /* Search results dropdown */
        .search-results {
            position: absolute;
            top: 45px;
            left: 15px;
            right: 15px;
            background: white;
            border-radius: 5px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s;
            z-index: 1050; /* Higher than other elements */
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .search-results.active {
            max-height: 300px;
            overflow-y: auto;
            padding: 5px 0;
            border: 1px solid #ddd;
        }
        .search-result-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s;
            cursor: pointer;
        }
        .search-result-item:hover {
            background-color: #f5f9fd;
            transform: translateX(3px);
            box-shadow: inset 3px 0 0 #3498db;
        }
        .search-result-item:last-child {
            border-bottom: none;
        }
        .search-result-item .badge {
            font-size: 10px;
            padding: 3px 8px;
            margin-left: 8px;
            border-radius: 10px;
            text-transform: uppercase;
            font-weight: 500;
        }
        .search-result-item .badge-agent {
            background-color: #3498db;
            color: white;
        }
        .search-result-item .badge-client {
            background-color: #2ecc71;
            color: white;
        }
        .search-result-item .search-result-details {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .search-result-item .search-result-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }
        .search-result-item .search-result-info {
            font-size: 12px;
            color: #777;
        }
        .search-no-results {
            padding: 15px;
            text-align: center;
            color: #777;
        }
        .sidebar-menu {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }
        .sidebar .nav-link {
            padding: 14px 20px;
            color: rgba(255,255,255,0.9); /* Brighter text for better readability */
            display: flex;
            align-items: center;
            transition: all 0.3s;
            border-radius: 10px;
            margin: 5px 0;
            font-weight: 500;
        }
        .sidebar .nav-link i {
            margin-right: 12px;
            width: 22px;
            text-align: center;
            font-size: 1.1em;
            transition: transform 0.3s ease;
            color: #3498db; /* Accent color for icons */
        }
        .sidebar .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: #fff;
            text-decoration: none;
            transform: translateX(5px);
        }
        .sidebar .nav-link:hover i {
            transform: scale(1.1);
            color: #ffffff; /* Icon becomes white on hover */
        }
        .sidebar .nav-link.active {
            color: #fff;
            background: rgba(52, 152, 219, 0.2); /* Subtle blue highlight */
            border-left: 4px solid #3498db; /* Professional blue accent */
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .sidebar .nav-link.active i {
            color: #ffffff; /* White icon for active state */
        }
        .logout-container {
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: auto;
        }
        .logout-btn {
            display: block;
            width: 100%;
            padding: 12px;
            text-align: center;
            background: #e74c3c; /* Clean red color */
            color: white;
            border: none;
            border-radius: 10px;
            transition: all 0.3s;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .logout-btn:hover {
            background: #c0392b; /* Darker red on hover */
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
            text-decoration: none;
            color: white;
        }
        .main-content {
            margin-left: 280px; /* Match sidebar width */
            padding: 25px;
            background-color: #f0f4f8;
         
            width: calc(100% - 280px); /* Ensure content takes remaining width */
            box-sizing: border-box; /* Include padding in width calculation */
        }
        .page-header {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .page-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #4a5568;
        }
        .card-header {
            background-color: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 20px;
            font-weight: 600;
            color: #4a5568;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
            background-color: white;
            margin-left: 300px;
    margin-right: 20px;
        }
        .btn-primary {
            background-color: #e31b23;
            border-color: #e31b23;
        }
        .btn-primary:hover {
            background-color: #c41920;
            border-color: #c41920;
        }
        .dashboard-cards {
            display: flex;
            flex-wrap: wrap;
            margin-left: 300px;
            margin-right: 20px;
            gap: 15px;
            margin-bottom: 20px;
        }
        .dashboard-card {
            flex: 1;
            min-width: 180px;
            max-width: 220px;
            background-color: #a8d8f0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            color: #333;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .dashboard-card h3 {
            font-size: 16px;
            margin-bottom: 10px;
        }
        .dashboard-card .value {
            font-size: 24px;
            font-weight: bold;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #4a5568;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo-container">
                <a href="dashboard.php">
                    <img src="assets/logo.png" alt="AmMetLife Logo">
                </a>
            </div>
            
            <!-- Add search bar to sidebar -->
            <div class="sidebar-search">
                <i class="fas fa-search"></i>
                <input type="text" id="sidebar-search-input" name="sidebar-search-input" placeholder="Search clients or agents..." autocomplete="off">
                <?php if ($_SERVER['REMOTE_ADDR'] === '127.0.0.1' || $_SERVER['REMOTE_ADDR'] === '::1'): ?>
                    <a href="test_search.php" target="_blank" class="search-debug-link" title="Debug Search" style="position: absolute; right: 10px; top: 10px; color: rgba(255,255,255,0.5); font-size: 12px;"><i class="fas fa-bug"></i></a>
                <?php endif; ?>
                <div class="search-results" id="search-results">
                    <!-- Search results will appear here -->
                </div>
            </div>
            
            <div class="sidebar-menu">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'agents.php' || strpos(basename($_SERVER['PHP_SELF']), 'agent_') === 0 ? 'active' : ''; ?>" href="agents.php">
                            <i class="fas fa-user-tie"></i> Agents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'clients.php' || strpos(basename($_SERVER['PHP_SELF']), 'client_') === 0 ? 'active' : ''; ?>" href="clients.php">
                            <i class="fas fa-users"></i> Clients
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="logout-container">
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt mr-2"></i> Logout
                </a>
            </div>
        </div>

        <!-- Main content area -->
        <div class="main-content">
            <!-- Page header with title -->
            <div class="page-header">
                <h1><?php echo $pageTitle ?? 'CRM System'; ?></h1>
            </div>
            
            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($_GET['success']); ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($_GET['error']); ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>
            
            <!-- Content will be placed here -->
        </div>
    </div>

    <!-- Search Functionality Script -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded');
        
        // Check if jQuery is loaded
        if (typeof jQuery !== 'undefined') {
            console.log('jQuery is available');
        } else {
            console.log('jQuery is NOT available');
        }
        
        // Get the search elements with pure JavaScript
        var searchInput = document.getElementById('sidebar-search-input');
        var searchResults = document.getElementById('search-results');
        
        if (!searchInput) {
            console.error('Search input element not found!');
            return;
        }
        
        if (!searchResults) {
            console.error('Search results element not found!');
            return;
        }
        
        console.log('Search elements found');
        
        // Debounce function to limit API calls
        function debounce(func, wait) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(function() {
                    func.apply(context, args);
                }, wait);
            };
        }
        
        // Function to perform the search
        function performSearch() {
            var query = searchInput.value.trim();
            console.log('Performing search for:', query);
            
            if (query === '') {
                searchResults.classList.remove('active');
                searchResults.innerHTML = '';
                return;
            }
            
            if (query.length < 2) {
                searchResults.classList.remove('active');
                searchResults.innerHTML = '<div class="search-no-results">Type at least 2 characters</div>';
                return;
            }
            
            // Make an XMLHttpRequest (no jQuery dependency)
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'live_search.php?query=' + encodeURIComponent(query), true);
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    console.log('Search response status:', xhr.status);
                    console.log('Search response text:', xhr.responseText);
                    
                    if (xhr.status === 200) {
                        try {
                            var data = JSON.parse(xhr.responseText);
                            displayResults(data);
                        } catch (e) {
                            console.error('Error parsing JSON:', e);
                            searchResults.innerHTML = '<div class="search-no-results">Error parsing results</div>';
                        }
                    } else {
                        searchResults.innerHTML = '<div class="search-no-results">Error: ' + xhr.status + '</div>';
                    }
                }
            };
            
            xhr.onerror = function() {
                console.error('Search request failed');
                searchResults.innerHTML = '<div class="search-no-results">Request failed</div>';
            };
            
            xhr.send();
            searchResults.innerHTML = '<div class="search-no-results">Searching...</div>';
            searchResults.classList.add('active');
        }
        
        // Function to display the search results
        function displayResults(data) {
            // Clear previous results
            searchResults.innerHTML = '';
            
            // Show results container
            searchResults.classList.add('active');
            
            // If no results
            if (!data || data.length === 0) {
                searchResults.innerHTML = '<div class="search-no-results">No results found</div>';
                return;
            }
            
            // Add each result to the results container
            data.forEach(function(item) {
                // Badge class based on type
                var badgeClass = item.type === 'agent' ? 'badge-agent' : 'badge-client';
                
                // Create result item element
                var resultItem = document.createElement('div');
                resultItem.className = 'search-result-item';
                
                // Make whole item clickable
                resultItem.setAttribute('data-href', item.type + '_view.php?id=' + encodeURIComponent(item.id));
                resultItem.style.cursor = 'pointer';
                
                // Add HTML content
                resultItem.innerHTML = 
                    '<div class="search-result-details">' +
                        '<div class="search-result-name">' +
                            escapeHtml(item.name) +
                            '<span class="badge ' + badgeClass + '">' + item.type + '</span>' +
                        '</div>' +
                        '<div class="search-result-info">' + escapeHtml(item.email || '') + '</div>' +
                    '</div>';
                
                // Add click event to the whole item
                resultItem.addEventListener('click', function(e) {
                    window.location.href = this.getAttribute('data-href');
                });
                
                searchResults.appendChild(resultItem);
            });
        }
        
        // Escape HTML to prevent XSS
        function escapeHtml(unsafe) {
            if (!unsafe) return '';
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }
        
        // Add event listeners
        var debouncedSearch = debounce(performSearch, 300);
        
        // Add input event listener
        searchInput.addEventListener('input', debouncedSearch);
        
        // Handle pressing Enter in the search input
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                
                // If there's at least one result, navigate to it
                var firstResult = searchResults.querySelector('.search-result-item .search-result-actions a');
                if (firstResult) {
                    window.location.href = firstResult.href;
                }
            }
        });
        
        // Close search results when clicking outside
        document.addEventListener('click', function(event) {
            if (event.target !== searchInput && !searchResults.contains(event.target)) {
                searchResults.classList.remove('active');
            }
        });
        
        console.log('Search event listeners attached');
    });
    </script>
</body>
</html>